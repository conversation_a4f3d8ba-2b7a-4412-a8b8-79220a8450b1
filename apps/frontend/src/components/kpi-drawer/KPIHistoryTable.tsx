import React, { memo } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { TrendingUp, TrendingDown, AlertCircle, Loader2 } from 'lucide-react';
import { useKpiHistory } from '@/hooks/useKpiHistory';
import { Button } from '@/components/ui/button';

interface Props {
  kpiId: string | null;
  timeframe?: string;
  currency?: string;
  clientId?: string;
  userId?: string;
  profileType?: string;
}

const KPIHistoryTable: React.FC<Props> = memo(({
  kpiId,
  timeframe = 'week',
  currency = 'all',
  clientId = 'default',
  userId = 'default_user',
  profileType
}) => {
  // Use real API data instead of mock data
  const {
    historyData,
    isLoading,
    error,
    refetch
  } = useKpiHistory({
    kpiId: kpiId || '',
    timeframe,
    currency,
    clientId,
    userId,
    profileType,
    enabled: !!kpiId
  });

  console.log('📊 KPI History Table - KPI ID:', kpiId, 'Loading:', isLoading, 'Error:', error);

  if (!kpiId) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <AlertCircle className="w-4 h-4" />
            Histórico do KPI
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Alert>
            <AlertDescription>
              Selecione um KPI para ver seu histórico.
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    );
  }

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TrendingUp className="w-4 h-4" />
            Histórico - {kpiId}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center p-6">
            <Loader2 className="w-6 h-6 animate-spin" />
            <span className="ml-2">Carregando dados históricos...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TrendingUp className="w-4 h-4" />
            Histórico - {kpiId}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              Erro ao carregar dados históricos: {error}
              <Button
                variant="link"
                onClick={() => refetch()}
                className="ml-2 p-0 h-auto underline"
              >
                Tentar novamente
              </Button>
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    );
  }

  // Process real history data
  const historyItems = historyData?.history_data || [];

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <TrendingUp className="w-4 h-4" />
          Histórico - {historyData?.kpi_name || kpiId}
        </CardTitle>
      </CardHeader>
      <CardContent>
        {historyItems.length > 0 ? (
          <div className="overflow-hidden rounded-lg border border-gray-200">
            <table className="w-full">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Data
                  </th>
                  <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Valor
                  </th>
                  <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Variação
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {historyItems.map((item, index) => {
                  const isPositive = item.status === 'positive' || item.change_percent > 0;
                  const formattedDate = new Date(item.period).toLocaleDateString('pt-BR', {
                    day: '2-digit',
                    month: '2-digit'
                  });

                  return (
                    <tr key={index} className="hover:bg-gray-50 transition-colors">
                      <td className="px-4 py-3 whitespace-nowrap">
                        <div className="flex items-center gap-2">
                          {isPositive ? (
                            <TrendingUp className="w-4 h-4 text-green-600" />
                          ) : (
                            <TrendingDown className="w-4 h-4 text-red-600" />
                          )}
                          <span className="text-sm font-medium text-gray-900">{formattedDate}</span>
                        </div>
                      </td>
                      <td className="px-4 py-3 whitespace-nowrap text-right">
                        <span className="text-sm font-semibold text-gray-900">{item.formatted_value}</span>
                      </td>
                      <td className="px-4 py-3 whitespace-nowrap text-right">
                        <span className={`text-sm font-medium ${isPositive ? 'text-green-600' : 'text-red-600'}`}>
                          {item.change_percent > 0 ? '+' : ''}{item.change_percent.toFixed(1)}%
                        </span>
                      </td>
                    </tr>
                  );
                })}
              </tbody>
            </table>
          </div>
        ) : (
          <Alert>
            <AlertCircle className="w-4 h-4" />
            <AlertDescription>
              Nenhum dado histórico disponível para o período selecionado.
            </AlertDescription>
          </Alert>
        )}

        {historyData && (
          <div className="mt-4 text-xs text-gray-500">
            Última atualização: {new Date(historyData.generated_at).toLocaleString('pt-BR')}
          </div>
        )}
      </CardContent>
    </Card>
  );
});

KPIHistoryTable.displayName = 'KPIHistoryTable';

export default KPIHistoryTable;
