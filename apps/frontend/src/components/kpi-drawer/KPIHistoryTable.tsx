import React, { memo } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { TrendingUp, TrendingDown, AlertCircle } from 'lucide-react';
import { Button } from '@/components/ui/button';

interface Props {
  kpiId: string | null;
  timeframe?: string;
  currency?: string;
  clientId?: string;
  userId?: string;
  profileType?: string;
}

const KPIHistoryTable: React.FC<Props> = memo(({
  kpiId,
  timeframe = 'week',
  currency = 'all',
  clientId = 'default',
  userId = 'default_user',
  profileType
}) => {
  // Temporary simplified version to avoid table import issues
  console.log('📊 KPI History Table - KPI ID:', kpiId);

  if (!kpiId) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <AlertCircle className="w-4 h-4" />
            Histórico do KPI
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Alert>
            <AlertDescription>
              Selecione um KPI para ver seu histórico.
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    );
  }

  // Mock data for testing - will be replaced with real API call
  const mockHistoryData = [
    { period: 'Hoje', value: 'R$ 2,45', variation: '-5.2%', status: 'positive' },
    { period: 'Ontem', value: 'R$ 2,58', variation: '+1.8%', status: 'positive' },
    { period: '2 dias atrás', value: 'R$ 2,54', variation: '-2.1%', status: 'negative' },
    { period: '3 dias atrás', value: 'R$ 2,59', variation: '+3.4%', status: 'positive' },
    { period: '4 dias atrás', value: 'R$ 2,51', variation: '-1.2%', status: 'negative' }
  ];

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <TrendingUp className="w-4 h-4" />
          Histórico - {kpiId}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          {mockHistoryData.map((item, index) => (
            <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
              <div className="flex items-center gap-3">
                {item.status === 'positive' ? (
                  <TrendingUp className="w-4 h-4 text-green-600" />
                ) : (
                  <TrendingDown className="w-4 h-4 text-red-600" />
                )}
                <span className="font-medium">{item.period}</span>
              </div>
              <div className="text-right">
                <div className="font-semibold">{item.value}</div>
                <div className={`text-sm ${item.status === 'positive' ? 'text-green-600' : 'text-red-600'}`}>
                  {item.variation}
                </div>
              </div>
            </div>
          ))}
        </div>
        <Alert className="mt-4">
          <AlertCircle className="w-4 h-4" />
          <AlertDescription>
            Dados de exemplo. A integração com a API de histórico será implementada em breve.
          </AlertDescription>
        </Alert>
      </CardContent>
    </Card>
  );
});

KPIHistoryTable.displayName = 'KPIHistoryTable';

export default KPIHistoryTable;
