import React, { memo, useMemo, useCallback } from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { TrendingUp, TrendingDown, Minus, RefreshCw, AlertCircle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import { useKpiHistory, KpiHistoryItem } from '@/hooks/useKpiHistory';

interface Props {
  kpiId: string | null;
  timeframe?: string;
  currency?: string;
  clientId?: string;
  userId?: string;
  profileType?: string;
}

const KPIHistoryTable: React.FC<Props> = memo(({
  kpiId,
  timeframe = 'week',
  currency = 'all',
  clientId = 'default',
  userId = 'default_user',
  profileType
}) => {
  // Fetch real history data using the custom hook
  const {
    historyData,
    isLoading,
    error,
    refetch,
    isRefetching,
    lastUpdated
  } = useKpiHistory({
    kpiId,
    timeframe,
    currency,
    clientId,
    userId,
    profileType,
    enabled: !!kpiId // Only fetch when kpiId is provided
  });

  // Memoized helper functions for status display
  const getStatusIcon = useCallback((status: string) => {
    switch (status) {
      case 'positive': return <TrendingUp className="w-3 h-3" />;
      case 'negative': return <TrendingDown className="w-3 h-3" />;
      default: return <Minus className="w-3 h-3" />;
    }
  }, []);

  const getStatusColor = useCallback((status: string) => {
    switch (status) {
      case 'positive': return 'text-green-600 bg-green-50';
      case 'negative': return 'text-red-600 bg-red-50';
      default: return 'text-gray-600 bg-gray-50';
    }
  }, []);

  const getStatusLabel = useCallback((status: string) => {
    switch (status) {
      case 'positive': return 'Alta';
      case 'negative': return 'Baixa';
      default: return 'Estável';
    }
  }, []);

  // Memoized format change percentage for display
  const formatChangePercent = useCallback((changePercent: number | null | undefined) => {
    if (changePercent === null || changePercent === undefined) {
      return 'N/A';
    }
    const sign = changePercent > 0 ? '+' : '';
    return `${sign}${changePercent.toFixed(1)}%`;
  }, []);

  // Memoized loading skeleton component
  const LoadingSkeleton = useMemo(() => (
    <TableBody>
      {[...Array(5)].map((_, index) => (
        <TableRow key={index} className="border-b-0">
          <TableCell className="border-r-0">
            <Skeleton className="h-4 w-20" data-testid="skeleton" />
          </TableCell>
          <TableCell className="border-r-0">
            <Skeleton className="h-4 w-16" data-testid="skeleton" />
          </TableCell>
          <TableCell className="border-r-0">
            <Skeleton className="h-4 w-12" data-testid="skeleton" />
          </TableCell>
          <TableCell className="text-right border-r-0">
            <Skeleton className="h-6 w-16 ml-auto" data-testid="skeleton" />
          </TableCell>
        </TableRow>
      ))}
    </TableBody>
  ), []);

  // Don't render if no kpiId provided
  if (!kpiId) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Histórico Detalhado</CardTitle>
        </CardHeader>
        <CardContent>
          <Alert>
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              Selecione um KPI para visualizar o histórico detalhado.
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-4">
        <div>
          <CardTitle>Histórico Detalhado</CardTitle>
          {historyData && (
            <p className="text-sm text-muted-foreground mt-1">
              {historyData.kpi_name} • {historyData.total_records} registros
              {lastUpdated && (
                <span className="ml-2">
                  • Atualizado {lastUpdated.toLocaleTimeString()}
                </span>
              )}
            </p>
          )}
        </div>
        <Button
          variant="outline"
          size="sm"
          onClick={refetch}
          disabled={isRefetching}
          className="ml-2"
        >
          <RefreshCw className={cn("h-4 w-4", isRefetching && "animate-spin")} />
          {isRefetching ? "Atualizando..." : "Atualizar"}
        </Button>
      </CardHeader>

      <CardContent className="p-0">
        {/* Error State */}
        {error && (
          <div className="p-4">
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                Erro ao carregar histórico: {error}
              </AlertDescription>
            </Alert>
          </div>
        )}

        {/* Loading or Content */}
        <Table>
          <TableHeader>
            <TableRow className="border-b-0 hover:bg-transparent">
              <TableHead className="border-r-0">Período</TableHead>
              <TableHead className="border-r-0">Valor</TableHead>
              <TableHead className="border-r-0">Variação</TableHead>
              <TableHead className="text-right border-r-0">Tendência</TableHead>
            </TableRow>
          </TableHeader>

          {/* Loading State */}
          {isLoading && <LoadingSkeleton />}

          {/* Data State */}
          {!isLoading && historyData && historyData.history_data.length > 0 && (
            <TableBody>
              {historyData.history_data.map((item: KpiHistoryItem, index: number) => (
                <TableRow key={`${item.period}-${index}`} className="border-b-0 hover:bg-gray-50">
                  <TableCell className="font-medium border-r-0">
                    {new Date(item.period).toLocaleDateString('pt-BR')}
                  </TableCell>
                  <TableCell className="border-r-0">
                    <span className="font-semibold">{item.formatted_value}</span>
                  </TableCell>
                  <TableCell className="border-r-0">
                    <span className={cn(
                      "font-medium",
                      item.change_percent && item.change_percent > 0 && "text-green-600",
                      item.change_percent && item.change_percent < 0 && "text-red-600",
                      (!item.change_percent || item.change_percent === 0) && "text-gray-600"
                    )}>
                      {formatChangePercent(item.change_percent)}
                    </span>
                  </TableCell>
                  <TableCell className="text-right border-r-0">
                    <div className={cn(
                      "inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium",
                      getStatusColor(item.status)
                    )}>
                      {getStatusIcon(item.status)}
                      <span>{getStatusLabel(item.status)}</span>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          )}

          {/* Empty State */}
          {!isLoading && historyData && historyData.history_data.length === 0 && (
            <TableBody>
              <TableRow>
                <TableCell colSpan={4} className="text-center py-8 text-muted-foreground">
                  Nenhum dado histórico encontrado para este período.
                </TableCell>
              </TableRow>
            </TableBody>
          )}
        </Table>
      </CardContent>
    </Card>
  );
});

// Display name for debugging
KPIHistoryTable.displayName = 'KPIHistoryTable';

export { KPIHistoryTable };