/**
 * KPI History Hook for DataHero4 Drawer Implementation
 * ===================================================
 * 
 * Custom hook for fetching and managing KPI historical data used in drawer components.
 * Provides real-time data fetching, loading states, error handling, and cache management.
 * 
 * Features:
 * - Real-time data fetching based on KPI ID and timeframe
 * - Loading and error states management
 * - Automatic refetch when parameters change
 * - Cache management for performance
 * - Integration with existing API structure
 * 
 * Author: DataHero4 Team
 * Date: 2025-01-29
 */

import { useState, useEffect, useCallback, useMemo } from 'react';
import { LoadingState, ErrorState } from '@/types/dashboard';

// API configuration
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'https://backend-production-9857.up.railway.app';

// Types for KPI History
export interface KpiHistoryItem {
  period: string;
  value: number;
  formatted_value: string;
  change_percent?: number;
  status: 'positive' | 'negative' | 'neutral';
  metadata?: Record<string, any>;
}

export interface KpiHistoryData {
  kpi_id: string;
  kpi_name: string;
  timeframe: string;
  currency: string;
  total_records: number;
  history_data: KpiHistoryItem[];
  calculation_metadata?: Record<string, any>;
  generated_at: string;
}

export interface UseKpiHistoryProps {
  kpiId: string | null;
  timeframe?: string;
  currency?: string;
  clientId?: string;
  userId?: string;
  profileType?: string;
  enabled?: boolean; // Allow disabling the hook
}

export interface UseKpiHistoryReturn extends LoadingState, ErrorState {
  historyData: KpiHistoryData | null;
  refetch: () => Promise<void>;
  isRefetching: boolean;
  lastUpdated: Date | null;
}

/**
 * Hook for fetching KPI historical data
 */
export const useKpiHistory = ({
  kpiId,
  timeframe = 'week',
  currency = 'all',
  clientId = 'default',
  userId = 'default_user',
  profileType,
  enabled = true
}: UseKpiHistoryProps): UseKpiHistoryReturn => {
  
  // State management
  const [historyData, setHistoryData] = useState<KpiHistoryData | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isRefetching, setIsRefetching] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);

  // Memoized fetch function
  const fetchHistoryData = useCallback(async (isRefetch = false) => {
    if (!kpiId || !enabled) {
      return;
    }

    try {
      if (isRefetch) {
        setIsRefetching(true);
      } else {
        setIsLoading(true);
      }
      setError(null);

      // Build query parameters
      const params = new URLSearchParams({
        timeframe,
        currency,
        client_id: clientId,
        user_id: userId,
        ...(profileType && { profile_type: profileType })
      });

      const url = `${API_BASE_URL}/api/v1/kpis/${kpiId}/history?${params}`;
      
      console.log(`📊 Fetching KPI history: ${kpiId} - ${timeframe}`);
      
      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(
          errorData.detail?.message || 
          errorData.message || 
          `HTTP ${response.status}: ${response.statusText}`
        );
      }

      const data: KpiHistoryData = await response.json();
      
      setHistoryData(data);
      setLastUpdated(new Date());
      
      console.log(`✅ KPI history loaded: ${data.total_records} records for ${kpiId}`);
      
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch KPI history';
      console.error(`❌ Error fetching KPI history for ${kpiId}:`, errorMessage);
      setError(errorMessage);
      setHistoryData(null);
    } finally {
      setIsLoading(false);
      setIsRefetching(false);
    }
  }, [kpiId, timeframe, currency, clientId, userId, profileType, enabled]);

  // Refetch function for manual refresh
  const refetch = useCallback(async () => {
    await fetchHistoryData(true);
  }, [fetchHistoryData]);

  // Effect for automatic fetching when parameters change
  useEffect(() => {
    if (kpiId && enabled) {
      fetchHistoryData(false);
    } else {
      // Clear data when disabled or no kpiId
      setHistoryData(null);
      setError(null);
      setLastUpdated(null);
    }
  }, [fetchHistoryData, kpiId, enabled]);

  // Memoized return value
  const returnValue = useMemo(() => ({
    historyData,
    isLoading,
    isRefetching,
    error,
    refetch,
    lastUpdated
  }), [historyData, isLoading, isRefetching, error, refetch, lastUpdated]);

  return returnValue;
};

/**
 * Hook for fetching multiple KPI histories (batch)
 */
export const useMultipleKpiHistory = (
  kpiIds: string[],
  options: Omit<UseKpiHistoryProps, 'kpiId'>
) => {
  const [histories, setHistories] = useState<Record<string, KpiHistoryData>>({});
  const [isLoading, setIsLoading] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});

  const fetchMultipleHistories = useCallback(async () => {
    if (kpiIds.length === 0 || !options.enabled) {
      return;
    }

    setIsLoading(true);
    const newHistories: Record<string, KpiHistoryData> = {};
    const newErrors: Record<string, string> = {};

    // Fetch all histories in parallel
    const promises = kpiIds.map(async (kpiId) => {
      try {
        const params = new URLSearchParams({
          timeframe: options.timeframe || 'week',
          currency: options.currency || 'all',
          client_id: options.clientId || 'default',
          user_id: options.userId || 'default_user',
          ...(options.profileType && { profile_type: options.profileType })
        });

        const url = `${API_BASE_URL}/api/v1/kpis/${kpiId}/history?${params}`;
        const response = await fetch(url);

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const data: KpiHistoryData = await response.json();
        newHistories[kpiId] = data;
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'Failed to fetch';
        newErrors[kpiId] = errorMessage;
      }
    });

    await Promise.all(promises);

    setHistories(newHistories);
    setErrors(newErrors);
    setIsLoading(false);
  }, [kpiIds, options]);

  useEffect(() => {
    fetchMultipleHistories();
  }, [fetchMultipleHistories]);

  return {
    histories,
    isLoading,
    errors,
    refetch: fetchMultipleHistories
  };
};

/**
 * Hook for real-time KPI history updates
 */
export const useRealtimeKpiHistory = (
  kpiId: string | null,
  options: Omit<UseKpiHistoryProps, 'kpiId'> & { 
    refreshInterval?: number;
    autoRefresh?: boolean;
  }
) => {
  const { refreshInterval = 30000, autoRefresh = false, ...historyOptions } = options;
  
  const historyResult = useKpiHistory({
    kpiId,
    ...historyOptions
  });

  // Auto-refresh effect
  useEffect(() => {
    if (!autoRefresh || !kpiId || !historyOptions.enabled) {
      return;
    }

    const interval = setInterval(() => {
      historyResult.refetch();
    }, refreshInterval);

    return () => clearInterval(interval);
  }, [autoRefresh, kpiId, refreshInterval, historyOptions.enabled, historyResult.refetch]);

  return historyResult;
};

export default useKpiHistory;
