import { useState, useEffect, useCallback } from 'react';
import { kpiEvents, KPIClickEvent } from '@/lib/events';

// Enhanced state interface for KPI Drawer
export interface KPIDrawerState {
  isOpen: boolean;
  currentKPI: string | null;
  selectedCardId: string | null;
  superAgentActive: boolean;
  originalElement: HTMLElement | null;
  originalRect: DOMRect | null;
}

export const useKPIDrawer = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [currentKPI, setCurrentKPI] = useState<string | null>(null);
  const [selectedCardId, setSelectedCardId] = useState<string | null>(null);
  const [superAgentActive, setSuperAgentActive] = useState(false);
  const [originalElement, setOriginalElement] = useState<HTMLElement | null>(null);
  const [originalRect, setOriginalRect] = useState<DOMRect | null>(null);

  // Helper function to check if a card is selected
  const isCardSelected = useCallback((kpiId: string) => {
    return selectedCardId === kpiId;
  }, [selectedCardId]);

  // Function to set selected card
  const setSelectedCard = useCallback((kpiId: string | null) => {
    setSelectedCardId(kpiId);
  }, []);

  useEffect(() => {
    const handleKPISelected = ({ kpiId, element, cardRect }: KPIClickEvent) => {
      setCurrentKPI(kpiId);
      setSelectedCardId(kpiId); // Set selected card when drawer opens
      setOriginalElement(element || null);
      setOriginalRect(cardRect || null);
      setIsOpen(true);
      setSuperAgentActive(false);
    };

    const handleDrawerClose = () => {
      setIsOpen(false);
      // Clear selected card when drawer closes
      setSelectedCardId(null);
      // Reset after animation completes
      setTimeout(() => {
        setOriginalElement(null);
        setOriginalRect(null);
      }, 300);
    };

    kpiEvents.on('kpi:selected', handleKPISelected);
    kpiEvents.on('drawer:close', handleDrawerClose);

    return () => {
      kpiEvents.off('kpi:selected', handleKPISelected);
      kpiEvents.off('drawer:close', handleDrawerClose);
    };
  }, []);

  return {
    isOpen,
    currentKPI,
    selectedCardId,
    superAgentActive,
    originalElement,
    originalRect,
    setSuperAgentActive,
    setSelectedCard,
    isCardSelected,
    closeDrawer: () => kpiEvents.closeDrawer()
  };
};