import React, { useState, useEffect } from 'react';
import { MessageSquare, User } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useNavigate } from 'react-router-dom';
import { getDashboardSnapshot, convertSnapshotToKpiData, type KpiData } from '@/lib/api';
import KpiBentoGrid from '@/components/dashboard/KpiBentoGrid';
import DashboardControls from '@/components/dashboard/DashboardControls';
import AddKpiModal from '@/components/dashboard/AddKpiModal';
import { KPIDrawer } from '@/components/kpi-drawer';
import { DashboardLoading, FilterLoading } from '@/components/dashboard/LoadingStates';
import { useKpis } from '@/hooks/useKpis';
import { useDashboardFilters, type TimeframeOption, type CurrencyOption } from '@/hooks/useDashboardFilters';
import { usePersonalization } from '@/hooks/usePersonalization';
import ProfileSetup from '@/components/profile/ProfileSetup';
import { ProfileType } from '@/types/profile';
import { kpiEvents } from '@/lib/events';

const Dashboard = () => {
  const navigate = useNavigate();
  const [showAddKpiModal, setShowAddKpiModal] = useState(false);
  const [showProfileSetup, setShowProfileSetup] = useState(false);
  const [selectedProfile, setSelectedProfile] = useState<ProfileType | null>(null);

  // Personalization hook
  const { userProfile, isProfileSetupRequired } = usePersonalization();

  // Dashboard state
  const { filters, updateTimeframe, updateCurrency } = useDashboardFilters();

  // Enhanced handlers that emit events for drawer synchronization
  const handleTimeframeChange = (timeframe: TimeframeOption) => {
    updateTimeframe(timeframe);

    // Emit event for drawer synchronization
    kpiEvents.emit('dashboard:filters-changed', {
      filters: { ...filters, timeframe },
      source: 'dashboard',
      timestamp: new Date().toISOString()
    });
  };

  const handleCurrencyChange = (currency: CurrencyOption) => {
    updateCurrency(currency);

    // Emit event for drawer synchronization
    kpiEvents.emit('dashboard:filters-changed', {
      filters: { ...filters, currency },
      source: 'dashboard',
      timestamp: new Date().toISOString()
    });
  };

  // Use personalized KPIs if profile is available
  const {
    kpis,
    isLoading,
    isInitialLoading,
    isFilterChanging,
    isRefreshing,
    error,
    togglePriority,
    refreshKpis
  } = useKpis({
    filters,
    enablePersonalization: true, // Re-enabled for proper functionality
    userId: 'test_user_ceo', // TODO: Get from auth context
    userProfile: userProfile,
    profileType: selectedProfile || userProfile?.profileType
  });

  const handleRefresh = async () => {
    try {
      await refreshKpis();
    } catch (error) {
      console.error('Error refreshing dashboard:', error);
    }
  };

  const handleExport = () => {
    // Implementar lógica de exportação
    console.log('Exportando dados...');
  };

  const handleAddKpi = () => {
    setShowAddKpiModal(true);
  };

  const handleKpisSelected = (kpiIds: string[]) => {
    console.log(`✅ ${kpiIds.length} KPIs selecionados:`, kpiIds);
    setShowAddKpiModal(false);
    // TODO: Implementar lógica para adicionar KPIs
  };

  const handleRemoveKpi = (kpiId: string) => {
    console.log(`🗑️ KPI ${kpiId} será removido`);
    // TODO: Implementar lógica para remover KPI
  };

  const handleProfileSetup = () => {
    setShowProfileSetup(true);
  };

  const handleProfileComplete = (profile: any) => {
    console.log('✅ Profile setup completed:', profile);
    setSelectedProfile(profile.profileType);
    setShowProfileSetup(false);
    // Refresh KPIs with new profile
    refreshKpis();
  };

  const handleProfileChange = (profileType: ProfileType) => {
    setSelectedProfile(profileType);
    // Refresh KPIs with new profile
    refreshKpis();
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header simples sem linha separadora */}
      <div className="bg-gray-50 px-6 py-4">
        <div className="max-w-7xl mx-auto flex items-center justify-between">
          <div className="flex items-center space-x-6">
            {/* Ícone do DataHero */}
            <div className="w-8 h-8 bg-gradient-to-br from-blue-600 to-purple-600 rounded-lg flex items-center justify-center">
              <span className="text-white font-bold text-sm">AI</span>
            </div>
            
            {/* Indicadores de status */}
            <div className="flex items-center space-x-4 text-sm text-gray-500">
              <div className="flex items-center space-x-2">
                <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                <span>Sistema Online</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-3 h-3 bg-blue-500 rounded-full animate-pulse"></div>
                <span>Atualizando</span>
              </div>
            </div>
          </div>
          
          {/* Profile selector and actions */}
          <div className="flex items-center gap-3">
            {/* Profile selector */}
            <div className="flex items-center gap-2">
              <User className="w-4 h-4 text-gray-500" />
              <select
                value={selectedProfile || userProfile?.profileType || ''}
                onChange={(e) => handleProfileChange(e.target.value as ProfileType)}
                className="px-3 py-1 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="">Selecionar Perfil</option>
                <option value="CEO">CEO</option>
                <option value="CFO">CFO</option>
                <option value="Operations">Operations</option>
                <option value="Trader">Trader</option>
                <option value="Risk_Manager">Risk Manager</option>
              </select>
            </div>

            {/* Setup profile button */}
            <Button
              variant="outline"
              size="sm"
              onClick={handleProfileSetup}
              className="flex items-center gap-2 hover:bg-gray-50 transition-colors"
            >
              <User className="w-4 h-4" />
              Configurar Perfil
            </Button>

            {/* Botão para ir às perguntas */}
            <Button
              variant="outline"
              onClick={() => navigate('/')}
              className="flex items-center gap-2 hover:bg-gray-50 transition-colors"
            >
              <MessageSquare className="w-4 h-4" />
              Fazer Perguntas
            </Button>
          </div>
        </div>
      </div>

      {/* Conteúdo principal com melhor espaçamento */}
      <div className="pb-8">
        <div className="max-w-7xl mx-auto px-6 lg:px-8">
          {/* Título e descrição */}
          <div className="mb-6">
            <h1 className="text-3xl font-bold text-gray-900">
              Métricas Críticas
              {(selectedProfile || userProfile?.profileType) && (
                <span className="text-lg font-normal text-blue-600 ml-2">
                  - {selectedProfile || userProfile?.profileType}
                </span>
              )}
            </h1>
            <p className="mt-2 text-gray-600">
              {(selectedProfile || userProfile?.profileType)
                ? `KPIs personalizados para perfil ${selectedProfile || userProfile?.profileType}`
                : '6 indicadores essenciais para acompanhamento executivo em tempo real'
              }
            </p>
          </div>

          {/* Controles do Dashboard */}
          <DashboardControls
            filters={filters}
            onTimeframeChange={handleTimeframeChange}
            onCurrencyChange={handleCurrencyChange}
            onRefresh={handleRefresh}
            onExport={handleExport}
            onAddKpi={handleAddKpi}
            isRefreshing={isRefreshing}
          />

          {/* Loading indicator when filters change */}
          {isFilterChanging && (
            <div className="mb-4">
              <FilterLoading />
            </div>
          )}

          {/* Grid de KPIs com Bento Layout */}
          {isInitialLoading ? (
            <DashboardLoading />
          ) : error ? (
            <div className="bg-red-50 border border-red-200 rounded-xl p-6 text-center">
              <h3 className="text-lg font-semibold text-red-800 mb-2">
                Erro ao carregar KPIs
              </h3>
              <p className="text-red-600 mb-4">{error}</p>
              <Button 
                onClick={handleRefresh}
                disabled={isRefreshing}
                className="bg-red-600 hover:bg-red-700 text-white"
              >
                {isRefreshing ? 'Carregando...' : 'Tentar novamente'}
              </Button>
            </div>
          ) : (
            <KpiBentoGrid
              kpis={kpis}
              filters={filters}
              onTogglePriority={togglePriority}
              onRemoveKpi={handleRemoveKpi}
              periodData={{
                currentDate: new Date().toISOString(),
                isSimulated: false
              }}
            />
          )}
        </div>
      </div>

      {/* Modal para adicionar KPIs */}
      <AddKpiModal
        isOpen={showAddKpiModal}
        onClose={() => setShowAddKpiModal(false)}
        onKpisSelected={handleKpisSelected}
        existingKpiIds={[]}
      />

      {/* Profile Setup Modal */}
      {showProfileSetup && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-xl font-semibold">Configurar Perfil</h2>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowProfileSetup(false)}
              >
                ✕
              </Button>
            </div>
            <ProfileSetup
              userId="test_user_ceo"
              onComplete={handleProfileComplete}
              onSkip={() => setShowProfileSetup(false)}
            />
          </div>
        </div>
      )}

      {/* KPI Drawer */}
      <KPIDrawer />
    </div>
  );
};

export default Dashboard;
