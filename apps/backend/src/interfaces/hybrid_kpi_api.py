"""
Hybrid KPI API for DataHero4 Week 3 Implementation
==================================================

FastAPI endpoints for the 4 fundamental hybrid KPIs:
- spread_income_detailed
- margem_liquida_operacional  
- custo_por_transacao
- tempo_processamento_medio

Features:
- Profile-aware routing through SmartQueryRouter
- Real-time and cached data serving
- Comprehensive error handling
- Performance monitoring

Author: DataHero4 Team
Date: 2025-01-21
"""

import logging
from typing import Dict, List, Any, Optional
from fastapi import APIRouter, HTTPException, Query, Depends
from pydantic import BaseModel, Field

from src.services.hybrid_kpi_service import get_hybrid_kpi_service
from src.services.profile_detector import get_profile_detector

logger = logging.getLogger(__name__)

# Create router
hybrid_kpi_router = APIRouter(prefix="/api/v1", tags=["Hybrid KPIs"])


class KpiRequest(BaseModel):
    """Request model for KPI calculation."""
    kpi_id: str = Field(..., description="KPI identifier")
    client_id: str = Field(default="L2M", description="Client identifier")
    user_id: str = Field(..., description="User identifier")
    timeframe: str = Field(default="week", description="Data timeframe")
    currency: str = Field(default="all", description="Currency filter")
    profile_type: Optional[str] = Field(None, description="User profile type")


class BatchKpiRequest(BaseModel):
    """Request model for batch KPI calculation."""
    kpi_ids: List[str] = Field(..., description="List of KPI identifiers")
    client_id: str = Field(default="L2M", description="Client identifier")
    user_id: str = Field(..., description="User identifier")
    timeframe: str = Field(default="week", description="Data timeframe")
    currency: str = Field(default="all", description="Currency filter")
    profile_type: Optional[str] = Field(None, description="User profile type")


class KpiResponse(BaseModel):
    """Response model for KPI calculation."""
    kpi_id: str
    currentValue: Optional[float]
    formattedValue: Optional[str]
    title: str
    description: str
    unit: str
    timeframe: str
    currency: str
    metadata: Dict[str, Any]
    source: str
    error: Optional[str] = None


def get_hybrid_service():
    """Dependency to get HybridKpiService instance."""
    return get_hybrid_kpi_service()


def get_profile_service():
    """Dependency to get ProfileDetector instance."""
    return get_profile_detector()


@hybrid_kpi_router.get("/kpis", summary="Get all KPIs")
async def get_kpis(
    client_id: str = Query("default", description="Client identifier"),
    user_id: str = Query("default_user", description="User identifier"),
    profile_type: Optional[str] = Query(None, description="User profile type")
):
    """
    Get all available KPIs with their current values.
    This endpoint is used by the dashboard to display KPI cards.

    NOTE: Temporarily returning mock data for testing while database is unavailable.
    """
    try:
        logger.info(f"📊 Getting all KPIs for client {client_id}, user {user_id}")

        # Mock KPI data for testing (will be replaced with real data when DB is available)
        kpi_results = [
            {
                "id": "spread_income_detailed",
                "name": "Spread Income Detalhado",
                "value": 125000.50,
                "formatted_value": "R$ 125.000,50",
                "variation": "+12.5%",
                "status": "positive",
                "category": "spread",
                "description": "Receita detalhada por spread por moeda e período",
                "last_updated": "2025-01-29T15:00:00Z"
            },
            {
                "id": "margem_liquida_operacional",
                "name": "Margem Líquida Operacional",
                "value": 8.75,
                "formatted_value": "8,75%",
                "variation": "+2.1%",
                "status": "positive",
                "category": "performance",
                "description": "Margem operacional líquida",
                "last_updated": "2025-01-29T15:00:00Z"
            },
            {
                "id": "custo_por_transacao",
                "name": "Custo por Transação",
                "value": 2.45,
                "formatted_value": "R$ 2,45",
                "variation": "-5.2%",
                "status": "positive",
                "category": "performance",
                "description": "Custo operacional médio por transação",
                "last_updated": "2025-01-29T15:00:00Z"
            },
            {
                "id": "tempo_processamento_medio",
                "name": "Tempo Processamento Médio",
                "value": 1.8,
                "formatted_value": "1,8s",
                "variation": "-8.3%",
                "status": "positive",
                "category": "performance",
                "description": "Tempo médio de processamento",
                "last_updated": "2025-01-29T15:00:00Z"
            }
        ]

        logger.info(f"✅ Successfully returned {len(kpi_results)} mock KPIs")
        return {"kpis": kpi_results}

    except Exception as e:
        logger.error(f"❌ Error getting KPIs: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail={
                'error': 'internal_server_error',
                'message': 'Failed to get KPIs',
                'details': str(e)
            }
        )


@hybrid_kpi_router.get("/supported", summary="Get supported hybrid KPIs")
async def get_supported_kpis():
    """Get list of supported hybrid KPIs."""
    return {
        "supported_kpis": [
            {
                "id": "spread_income_detailed",
                "name": "Spread Income Detalhado",
                "description": "Receita detalhada por spread por moeda e período",
                "category": "spread",
                "priority": "critical"
            },
            {
                "id": "margem_liquida_operacional", 
                "name": "Margem Líquida Operacional",
                "description": "Margem operacional líquida: (Receita Spread - Custos Operacionais) / Receita Total * 100",
                "category": "performance",
                "priority": "critical"
            },
            {
                "id": "custo_por_transacao",
                "name": "Custo por Transação", 
                "description": "Custo operacional médio por transação processada",
                "category": "performance",
                "priority": "high"
            },
            {
                "id": "tempo_processamento_medio",
                "name": "Tempo Processamento Médio",
                "description": "Tempo médio de processamento de transações (em segundos)",
                "category": "performance", 
                "priority": "high"
            }
        ],
        "total_kpis": 4,
        "architecture": "hybrid_3_layer"
    }


@hybrid_kpi_router.post("/calculate", response_model=KpiResponse, summary="Calculate single hybrid KPI")
async def calculate_kpi(
    request: KpiRequest,
    service: get_hybrid_service = Depends()
):
    """
    Calculate a single hybrid KPI using the SmartQueryRouter.
    
    The KPI will be routed through the optimal layer based on user profile:
    - Layer 1: Profile-aware snapshots (CEO, CFO)
    - Layer 2: Personalized cache (Trader, Operations)
    - Layer 3: Direct optimized queries (Risk_Manager)
    """
    try:
        logger.info(f"🧮 API request for KPI {request.kpi_id} by user {request.user_id}")
        
        # Calculate KPI using hybrid service
        result = service.calculate_kpi(
            kpi_id=request.kpi_id,
            client_id=request.client_id,
            user_id=request.user_id,
            timeframe=request.timeframe,
            currency=request.currency,
            profile_type=request.profile_type
        )
        
        # Check for errors
        if result.get('error'):
            raise HTTPException(
                status_code=400,
                detail={
                    'error': result['error'],
                    'message': result.get('message', 'KPI calculation failed'),
                    'kpi_id': request.kpi_id
                }
            )
        
        # Return successful result
        return KpiResponse(
            kpi_id=result['kpi_id'],
            currentValue=result.get('currentValue'),
            formattedValue=result.get('formattedValue'),
            title=result.get('title', ''),
            description=result.get('description', ''),
            unit=result.get('unit', ''),
            timeframe=result.get('timeframe', request.timeframe),
            currency=result.get('currency', request.currency),
            metadata=result.get('metadata', {}),
            source=result.get('source', 'hybrid')
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ API error calculating KPI {request.kpi_id}: {e}")
        raise HTTPException(
            status_code=500,
            detail={
                'error': 'api_error',
                'message': str(e),
                'kpi_id': request.kpi_id
            }
        )


@hybrid_kpi_router.post("/calculate-batch", summary="Calculate multiple hybrid KPIs")
async def calculate_batch_kpis(
    request: BatchKpiRequest,
    service: get_hybrid_service = Depends()
):
    """
    Calculate multiple hybrid KPIs in batch using the SmartQueryRouter.
    
    Each KPI will be routed through the optimal layer based on user profile
    and KPI characteristics. Returns results for all requested KPIs.
    """
    try:
        logger.info(f"🧮 API batch request for {len(request.kpi_ids)} KPIs by user {request.user_id}")
        
        # Calculate KPIs using hybrid service
        results = service.calculate_multiple_kpis(
            kpi_ids=request.kpi_ids,
            client_id=request.client_id,
            user_id=request.user_id,
            timeframe=request.timeframe,
            currency=request.currency,
            profile_type=request.profile_type
        )
        
        # Format response
        kpi_results = {}
        for kpi_id, result in results['kpis'].items():
            if result.get('error'):
                kpi_results[kpi_id] = {
                    'kpi_id': kpi_id,
                    'error': result['error'],
                    'message': result.get('message', 'KPI calculation failed')
                }
            else:
                kpi_results[kpi_id] = {
                    'kpi_id': result['kpi_id'],
                    'currentValue': result.get('currentValue'),
                    'formattedValue': result.get('formattedValue'),
                    'title': result.get('title', ''),
                    'description': result.get('description', ''),
                    'unit': result.get('unit', ''),
                    'timeframe': result.get('timeframe', request.timeframe),
                    'currency': result.get('currency', request.currency),
                    'metadata': result.get('metadata', {}),
                    'source': result.get('source', 'hybrid')
                }
        
        return {
            'kpis': kpi_results,
            'batch_metadata': results.get('batch_metadata', {}),
            'request_info': {
                'user_id': request.user_id,
                'timeframe': request.timeframe,
                'currency': request.currency,
                'profile_type': request.profile_type
            }
        }
        
    except Exception as e:
        logger.error(f"❌ API error in batch calculation: {e}")
        raise HTTPException(
            status_code=500,
            detail={
                'error': 'batch_api_error',
                'message': str(e),
                'requested_kpis': request.kpi_ids
            }
        )


@hybrid_kpi_router.get("/profile/{user_id}/detect", summary="Detect user profile")
async def detect_user_profile(
    user_id: str,
    analysis_days: int = Query(30, description="Number of days to analyze"),
    profile_service: get_profile_service = Depends()
):
    """
    Detect user profile based on query patterns and usage behavior.
    
    This endpoint uses the ProfileDetector to analyze user behavior
    and suggest the most appropriate profile for KPI personalization.
    """
    try:
        logger.info(f"🔍 API request to detect profile for user {user_id}")
        
        # Detect profile
        detection_result = profile_service.detect_profile(
            user_id=user_id,
            analysis_days=analysis_days
        )
        
        return {
            'user_id': user_id,
            'detected_profile': detection_result.get('detected_profile'),
            'confidence': detection_result.get('confidence', 0.0),
            'reason': detection_result.get('reason', 'unknown'),
            'analysis': detection_result.get('analysis', {}),
            'recommendations': {
                'should_use_profile': detection_result.get('confidence', 0.0) >= 0.30,
                'suggested_kpis': _get_profile_kpi_recommendations(
                    detection_result.get('detected_profile')
                )
            }
        }
        
    except Exception as e:
        logger.error(f"❌ API error detecting profile for user {user_id}: {e}")
        raise HTTPException(
            status_code=500,
            detail={
                'error': 'profile_detection_error',
                'message': str(e),
                'user_id': user_id
            }
        )


@hybrid_kpi_router.get("/routing/stats", summary="Get routing statistics")
async def get_routing_stats(
    service: get_hybrid_service = Depends()
):
    """
    Get routing statistics and performance metrics from the SmartQueryRouter.
    
    Provides insights into cache hit rates, layer usage, and performance
    metrics for monitoring the hybrid architecture.
    """
    try:
        stats = service.router.get_routing_stats()
        
        return {
            'routing_stats': stats,
            'hybrid_kpis': [
                'spread_income_detailed',
                'margem_liquida_operacional', 
                'custo_por_transacao',
                'tempo_processamento_medio'
            ],
            'architecture_info': {
                'layers': ['snapshot', 'cache', 'direct'],
                'profiles': ['CEO', 'CFO', 'Risk_Manager', 'Trader', 'Operations'],
                'fail_fast': True,
                'fallback_enabled': False
            }
        }
        
    except Exception as e:
        logger.error(f"❌ API error getting routing stats: {e}")
        raise HTTPException(
            status_code=500,
            detail={
                'error': 'stats_error',
                'message': str(e)
            }
        )


def _get_profile_kpi_recommendations(profile_type: Optional[str]) -> List[str]:
    """Get KPI recommendations for a profile type."""
    if not profile_type:
        return []

    recommendations = {
        'CEO': ['spread_income_detailed', 'margem_liquida_operacional'],
        'CFO': ['margem_liquida_operacional', 'custo_por_transacao'],
        'Risk_Manager': ['tempo_processamento_medio'],
        'Trader': ['spread_income_detailed', 'tempo_processamento_medio'],
        'Operations': ['custo_por_transacao', 'tempo_processamento_medio']
    }

    return recommendations.get(profile_type, [])


# KPI History Models
class KpiHistoryItem(BaseModel):
    """Single item in KPI history."""
    period: str = Field(..., description="Period identifier (date/time)")
    value: float = Field(..., description="KPI value for this period")
    formatted_value: str = Field(..., description="Formatted value for display")
    change_percent: Optional[float] = Field(None, description="Percentage change from previous period")
    status: str = Field(..., description="Status: positive, negative, or neutral")
    metadata: Optional[Dict[str, Any]] = Field(None, description="Additional metadata")


class KpiHistoryResponse(BaseModel):
    """Response model for KPI history data."""
    kpi_id: str = Field(..., description="KPI identifier")
    kpi_name: str = Field(..., description="Human-readable KPI name")
    timeframe: str = Field(..., description="Timeframe used for history")
    currency: str = Field(..., description="Currency filter applied")
    total_records: int = Field(..., description="Total number of history records")
    history_data: List[KpiHistoryItem] = Field(..., description="Historical data points")
    calculation_metadata: Optional[Dict[str, Any]] = Field(None, description="Metadata about the calculation")
    generated_at: str = Field(..., description="Timestamp when data was generated")


@hybrid_kpi_router.get("/kpis/{kpi_id}/history", response_model=KpiHistoryResponse, summary="Get KPI history data")
async def get_kpi_history(
    kpi_id: str,
    timeframe: str = Query("week", description="Timeframe for history data"),
    currency: str = Query("all", description="Currency filter"),
    client_id: str = Query("default", description="Client identifier"),
    user_id: str = Query("default_user", description="User identifier"),
    profile_type: Optional[str] = Query(None, description="User profile type"),
    service: get_hybrid_service = Depends()
):
    """
    Get detailed history data for a specific KPI.

    Returns the tabular data used to calculate the KPI, showing historical values
    and trends over the specified timeframe. This data feeds into the drawer's
    history table component.

    Args:
        kpi_id: KPI identifier (e.g., 'total_volume', 'average_spread')
        timeframe: Time period ('1d', 'week', 'month', 'quarter')
        currency: Currency filter ('all', 'usd', 'eur', 'gbp')
        client_id: Client identifier
        user_id: User identifier
        profile_type: User profile type for optimization

    Returns:
        KpiHistoryResponse with historical data points
    """
    try:
        logger.info(f"📊 History request for KPI {kpi_id} - timeframe: {timeframe}, currency: {currency}")

        # Get KPI history using the hybrid service
        # This will be implemented in the KpiHistoryService
        history_result = service.get_kpi_history(
            kpi_id=kpi_id,
            client_id=client_id,
            user_id=user_id,
            timeframe=timeframe,
            currency=currency,
            profile_type=profile_type
        )

        # Check for errors
        if history_result.get('error'):
            raise HTTPException(
                status_code=400,
                detail={
                    'error': history_result['error'],
                    'message': history_result.get('message', 'Failed to get KPI history'),
                    'kpi_id': kpi_id
                }
            )

        logger.info(f"✅ Successfully retrieved history for KPI {kpi_id}")
        return history_result

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ Error getting KPI history for {kpi_id}: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail={
                'error': 'internal_server_error',
                'message': f'Failed to get history for KPI {kpi_id}',
                'details': str(e)
            }
        )
